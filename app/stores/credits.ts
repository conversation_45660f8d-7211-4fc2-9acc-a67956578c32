import { defineStore } from 'pinia'
import { useProductStore } from '~/stores/product'

interface CryptomusOrderResponse {
  success: boolean
  order_id: string
  order_uuid: string
  approval_url: string
  message: string
  error_code: string
}

export const useCreditsStore = defineStore('creditsStore', {
  state: () => ({
    locale: 'en',
    showDrawer: false,
    numberOfCreditsWanted: 0,
    isLoading: false,
    loadings: {} as Record<string, boolean>,
    errors: {} as Record<string, any>,
    orderUUID: '' as string,
    orderID: '' as string
  }),
  getters: {
    buyCreditProduct(): any {
      const product = useProductStore().buyCreditProduct
      return product || {}
    },
    creditUnitPrice(): number {
      // New rate: $1 per 100 base credits
      return 1
    },
    // Get credits with promotion applied
    getCreditsWithPromotion(): (baseCredits: number) => number {
      return (baseCredits: number) => {
        const productStore = useProductStore()
        if (productStore.isPromotionActive) {
          return baseCredits * (1 + productStore.getPromotionBonusPercentage / 100)
        }
        return baseCredits
      }
    },
    quickTopUpList(): any {
      return [1, 2, 5, 7, 10, 12, 15, 17, 20].map(num => {
        // New rate: $1 = 100 base credits
        const baseCredits = num * 100
        const creditsWithPromotion = this.getCreditsWithPromotion(baseCredits)
        return {
          credits: creditsWithPromotion,
          baseCredits: baseCredits,
          price: num, // $1, $2, $5, etc.
          quantity: num,
          hasPromotion: useProductStore().isPromotionActive
        }
      })
    },
    quantityWanted(): number {
      // Calculate base credits from total credits (accounting for promotion)
      const productStore = useProductStore()
      const baseCredits = productStore.isPromotionActive
        ? this.numberOfCreditsWanted / (1 + productStore.getPromotionBonusPercentage / 100)
        : this.numberOfCreditsWanted

      // Each unit = 100 base credits ($1)
      return Math.ceil(baseCredits / 100)
    }
  },

  actions: {
    processBuyCredits(numOfCredits: number) {
      this.numberOfCreditsWanted = numOfCredits
      this.showDrawer = true
    },

    async processStripePayment() {
      try {
        this.loadings.createStripeOrder = true
        const { apiService } = useAPI()
        const toast = useToast()

        // Calculate quantity based on the credits wanted (use quantityWanted getter)
        const quantity = this.quantityWanted

        // use the selected credit product ID
        const product = this.buyCreditProduct
        const requestPayload = { product_id: product.id, quantity }

        const response = await apiService.post(
          '/order/stripe/create',
          requestPayload
        )

        if (response.data.success && response.data.approval_url) {
          // Open approval URL in new window
          // window.open(response.data.approval_url, "_blank");
          const newWindow = popupCenter(600, 700, response.data.approval_url)

          if (window.focus) newWindow?.focus()

          const interval = setInterval(() => {
            if (newWindow?.closed) {
              clearInterval(interval)
              const authStore = useAuthStore()
              // Refresh user credits after payment is completed
              setTimeout(() => {
                authStore.userMe()
              }, 3000)
            }
          }, 500)

          // Close the drawer
          this.showDrawer = false
        } else {
          // Handle API error response
          toast.add({
            id: 'stripe-error',
            title: 'Payment Error',
            description:
              response.data.message || 'Failed to initiate Stripe payment',
            color: 'error'
          })
        }
      } catch (error: any) {
        const toast = useToast()
        toast.add({
          id: 'stripe-error',
          title: 'Payment Error',
          description:
            error.response?.data?.message || 'Failed to process Stripe payment',
          color: 'error'
        })
      } finally {
        this.loadings.createStripeOrder = false
      }
    },

    async createCryptoOrder() {
      this.isLoading = true
      const toast = useToast()

      try {
        const { apiService } = useAPI()

        // Calculate quantity based on the number of credit packages being purchased
        const quantity = this.quantityWanted

        // use the selected credit product ID
        const product = this.buyCreditProduct
        const payload = { product_id: product.id, quantity }

        const response = await apiService.post(
          '/order/cryptomus/create',
          payload
        )
        const responseData: CryptomusOrderResponse = response.data

        // Handle API response
        if (responseData.success) {
          const newWindow = popupCenter(600, 700, response.data.approval_url)

          if (window.focus) newWindow?.focus()

          const interval = setInterval(() => {
            if (newWindow?.closed) {
              clearInterval(interval)
              const authStore = useAuthStore()
              // Refresh user credits after payment is completed
              setTimeout(() => {
                authStore.userMe()
              }, 3000)
            }
          }, 500)
          this.showDrawer = false
        } else {
          toast.add({
            id: 'crypto-order-error',
            title: 'Payment Error',
            description:
              responseData.message
              || 'Failed to create crypto payment order. Please try again.',
            color: 'error'
          })
        }
        return responseData
      } catch (error: any) {
        console.error('🚀 ~ createCryptoOrder error:', error)

        toast.add({
          id: 'crypto-order-error',
          title: 'Payment Error',
          description:
            error.response?.data?.message
            || error.message
            || 'Failed to create crypto payment order. Please try again.',
          color: 'error'
        })

        return null
      } finally {
        this.isLoading = false
      }
    },

    async createPaypalOrder(payload: object) {
      try {
        this.loadings['createPaypalOrder'] = true
        this.errors = {}
        const { apiService } = useAPI()
        const response = await apiService.post('/order/paypal/create', payload)
        this.orderUUID = response.data.order_uuid
        this.orderID = response.data.order_id
        return response.data.order_id
      } catch (error: any) {
        this.errors['createPaypalOrder'] = {
          message: 'Something went wrong',
          error_code: error.response?.data?.error_code || 'SYSTEM_ERROR'
        }
        return false
      } finally {
        this.loadings['createPaypalOrder'] = false
      }
    },

    async approvePaypalOrder() {
      try {
        this.loadings['approvePaypalOrder'] = true
        this.errors = {}
        const { apiService } = useAPI()
        const response = await apiService.put(
          `/order/paypal/${this.orderUUID}/complete`,
          {
            order_id: this.orderID
          }
        )
        if (response.data) {
          return response.data
        }
      } catch (error: any) {
        this.errors['approvePaypalOrder'] = {
          message: 'Something went wrong',
          error_code: error.response?.data?.error_code || 'SYSTEM_ERROR'
        }
        return false
      } finally {
        this.loadings['approvePaypalOrder'] = false
      }
    },

    async cancelPaypalOrder() {
      try {
        this.loadings['cancelPaypalOrder'] = true
        this.errors = {}
        const { apiService } = useAPI()
        const response = await apiService.put(
          `/order/paypal/${this.orderUUID}/cancel`
        )
        if (response.data) {
          return response.data
        }
      } catch (error: any) {
        this.errors['cancelPaypalOrder'] = {
          message: 'Something went wrong',
          error_code: error.response?.data?.error_code || 'SYSTEM_ERROR'
        }
        return false
      } finally {
        this.loadings['cancelPaypalOrder'] = false
      }
    }
  }
})
