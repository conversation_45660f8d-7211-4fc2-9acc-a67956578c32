<template>
  <USlideover
    v-model:open="showDrawer"
    :title="$t('buyCredits.checkout')"
    :description="$t('buyCredits.checkoutDescription')"
  >
    <template #body>
      <div class="space-y-6 w-full">
        <!-- Promotion Banner -->
        <div
          v-if="productStore.isPromotionActive"
          class="bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4"
        >
          <div class="flex items-center gap-3">
            <UIcon
              name="i-lucide-gift"
              class="size-6 text-amber-600 dark:text-amber-400"
            />
            <div>
              <h4 class="font-semibold text-amber-800 dark:text-amber-200">
                {{ $t("Special Promotion - 100% Bonus Credits!") }}
              </h4>
              <p class="text-sm text-amber-700 dark:text-amber-300">
                {{ $t("Get double credits with every purchase. Limited time offer!") }}
              </p>
            </div>
          </div>
        </div>

        <!-- Order Detail Section -->
        <div>
          <h3 class="text-base font-semibold mb-4">
            {{ $t("buyCredits.orderDetail") }}
          </h3>
          <div
            class="bg-neutral-50 dark:bg-neutral-800 rounded-lg p-4 space-y-3"
          >
            <div class="flex justify-between items-center">
              <span class="text-neutral-600 dark:text-neutral-400">{{ $t("buyCredits.credits") }}:</span>
              <div class="text-right">
                <div class="font-medium">{{
                  formatNumber(numberOfCreditsWanted)
                }}</div>
                <div
                  v-if="productStore.isPromotionActive && bonusCredits > 0"
                  class="text-xs text-amber-600 dark:text-amber-400"
                >
                  {{ $t("Base: {base} + Bonus: {bonus}", {
                    base: formatNumber(baseCreditsAmount),
                    bonus: formatNumber(bonusCredits)
                  }) }}
                </div>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-neutral-600 dark:text-neutral-400">{{ $t("buyCredits.pricePerUnit") }}:</span>
              <span class="font-medium">${{ unitPrice }}</span>
            </div>
            <UDivider />
            <div
              class="flex justify-between items-center text-base font-semibold"
            >
              <span>{{ $t("buyCredits.totalCredits") }}:</span>
              <span class="text-primary-600 text-lg">{{ formatNumber(numberOfCreditsWanted) }}</span>
            </div>
            <div
              class="flex justify-between items-center text-base font-semibold"
            >
              <span>{{ $t("buyCredits.totalPrice") }}:</span>
              <span class="text-primary-600 text-lg">${{ totalPrice }}</span>
            </div>
          </div>
        </div>

        <!-- Payment Section -->
        <div>
          <h3 class="text-base font-semibold mb-4">
            {{ $t("buyCredits.payment") }}
          </h3>
          <div class="space-y-3">
            <PayWithCrypto />
            <!-- <PayWithCard /> -->
            <PayWithStripe />
            <PayWithPaypal />
          </div>
        </div>
      </div>
    </template>

    <template #footer>
      <UButton
        :label="$t('buyCredits.cancel')"
        color="neutral"
        variant="outline"
        class="justify-center"
        block
        @click="showDrawer = false"
      />
    </template>
  </USlideover>
</template>

<script setup lang="ts">
const creditsStore = useCreditsStore()
const productStore = useProductStore()
const { showDrawer, numberOfCreditsWanted, creditUnitPrice, buyCreditProduct }
  = storeToRefs(creditsStore)

// Calculate unit price based on how credits are priced
const unitPrice = computed(() => {
  return (creditUnitPrice.value / buyCreditProduct.value.base_credit).toFixed(
    6
  )
})

// Calculate total price for the selected credits
const totalPrice = computed(() => {
  return (
    (numberOfCreditsWanted.value / buyCreditProduct.value.base_credit)
    * creditUnitPrice.value
  ).toFixed(2)
})

// Calculate base credits (without promotion)
const baseCreditsAmount = computed(() => {
  if (!productStore.isPromotionActive) {
    return numberOfCreditsWanted.value
  }
  // If promotion is active, calculate base credits from total
  const promotionMultiplier = 1 + productStore.getPromotionBonusPercentage / 100
  return Math.round(numberOfCreditsWanted.value / promotionMultiplier)
})

// Calculate bonus credits from promotion
const bonusCredits = computed(() => {
  if (!productStore.isPromotionActive) {
    return 0
  }
  return numberOfCreditsWanted.value - baseCreditsAmount.value
})
</script>
